#!/usr/bin/env python3
"""
Test script for HUD/overlay removal preprocessing functions.
This script tests the preprocessing functions to ensure they work correctly.
"""

import cv2
import numpy as np
import os
import sys

# Add the backend directory to the path so we can import from routes
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.pavement import detect_hud_regions, inpaint_rects, mask_inpaint_borders, preprocess_for_potholes

def create_test_image_with_hud():
    """Create a synthetic test image with HUD overlays for testing"""
    # Create a road-like image (gray with some texture)
    height, width = 480, 640
    img = np.ones((height, width, 3), dtype=np.uint8) * 120  # Gray background
    
    # Add some road texture
    for i in range(0, height, 20):
        cv2.line(img, (0, i), (width, i), (100, 100, 100), 1)
    
    # Add bottom banner (GPS overlay)
    banner_height = 60
    cv2.rectangle(img, (0, height - banner_height), (width, height), (30, 30, 30), -1)
    cv2.putText(img, "GPS: 1.234567, 103.123456", (10, height - 35), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    cv2.putText(img, "Speed: 45 km/h", (10, height - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # Add corner mini-map
    map_size = 80
    cv2.rectangle(img, (width - map_size - 10, height - map_size - 70), 
                  (width - 10, height - 70), (40, 40, 40), -1)
    cv2.putText(img, "MAP", (width - map_size + 10, height - 80), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Add some road markings that should be preserved
    cv2.line(img, (width//2 - 2, 0), (width//2 - 2, height - banner_height), (255, 255, 255), 4)
    cv2.line(img, (width//2 + 2, 0), (width//2 + 2, height - banner_height), (255, 255, 255), 4)
    
    return img

def test_hud_detection():
    """Test HUD region detection"""
    print("Testing HUD region detection...")
    
    # Create test image
    test_img = create_test_image_with_hud()
    
    # Detect HUD regions
    hud_rects = detect_hud_regions(test_img)
    
    print(f"Detected {len(hud_rects)} HUD regions:")
    for i, (x, y, w, h) in enumerate(hud_rects):
        print(f"  Region {i+1}: x={x}, y={y}, w={w}, h={h}")
    
    # Visualize detected regions
    debug_img = test_img.copy()
    for (x, y, w, h) in hud_rects:
        cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 0, 255), 2)
    
    return test_img, debug_img, hud_rects

def test_preprocessing_pipeline():
    """Test the complete preprocessing pipeline"""
    print("\nTesting complete preprocessing pipeline...")
    
    # Create test image
    test_img = create_test_image_with_hud()
    
    # Apply preprocessing
    cleaned_img, debug_img, hud_mask, edge_mask = preprocess_for_potholes(test_img)
    
    print("Preprocessing completed successfully!")
    print(f"Original image shape: {test_img.shape}")
    print(f"Cleaned image shape: {cleaned_img.shape}")
    print(f"HUD mask shape: {hud_mask.shape}")
    print(f"Edge mask shape: {edge_mask.shape}")
    
    return test_img, cleaned_img, debug_img, hud_mask, edge_mask

def save_test_results(original, cleaned, debug, hud_mask, edge_mask):
    """Save test results to files"""
    output_dir = "test_preprocessing_output"
    os.makedirs(output_dir, exist_ok=True)
    
    cv2.imwrite(os.path.join(output_dir, "01_original.jpg"), original)
    cv2.imwrite(os.path.join(output_dir, "02_cleaned.jpg"), cleaned)
    cv2.imwrite(os.path.join(output_dir, "03_debug_hud_detection.jpg"), debug)
    cv2.imwrite(os.path.join(output_dir, "04_hud_mask.jpg"), hud_mask)
    cv2.imwrite(os.path.join(output_dir, "05_edge_mask.jpg"), edge_mask)
    
    print(f"\nTest results saved to {output_dir}/")
    print("Files saved:")
    print("  01_original.jpg - Original test image with HUD overlays")
    print("  02_cleaned.jpg - Cleaned image after preprocessing")
    print("  03_debug_hud_detection.jpg - Debug image showing detected HUD regions")
    print("  04_hud_mask.jpg - Mask showing HUD regions that were inpainted")
    print("  05_edge_mask.jpg - Mask showing border regions that were inpainted")

def main():
    """Main test function"""
    print("=== HUD/Overlay Removal Preprocessing Test ===\n")
    
    try:
        # Test HUD detection
        original, debug, hud_rects = test_hud_detection()
        
        # Test complete pipeline
        original, cleaned, debug, hud_mask, edge_mask = test_preprocessing_pipeline()
        
        # Save results
        save_test_results(original, cleaned, debug, hud_mask, edge_mask)
        
        print("\n=== Test completed successfully! ===")
        print("The preprocessing functions are working correctly.")
        print("Check the output images to verify the HUD/overlay removal.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
